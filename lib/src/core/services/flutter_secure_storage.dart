import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '/src/domain/models/login.dart';

class SessionManager {
  final _storage = const FlutterSecureStorage(
    webOptions: WebOptions(
      dbName: 'neorevv_secure_storage',
      publicKey: 'neorevv_public_key',
    ),
  );

  // Save session data
  Future<void> saveSession(LoginModel loginModel) async {
    try {
      await _storage.write(key: 'authToken', value: loginModel.jwt);
      await _storage.write(key: 'refreshToken', value: loginModel.refreshToken);
    } catch (e) {
      if (kIsWeb) {
        debugPrint('FlutterSecureStorage error on web: $e');
        // For web without HTTPS, fall back to regular storage (not recommended for production)
        // You might want to show a warning to the user about security
        throw Exception('Secure storage not available. Please use HTTPS for secure authentication.');
      }
      rethrow;
    }
  }

  // Get token
  Future<String?> getToken() async {
    try {
      return await _storage.read(key: 'authToken');
    } catch (e) {
      if (kIsWeb) {
        debugPrint('FlutterSecureStorage error on web: $e');
        return null;
      }
      rethrow;
    }
  }

  Future<String?> getRefreshToken() async {
    try {
      return await _storage.read(key: 'refreshToken');
    } catch (e) {
      if (kIsWeb) {
        debugPrint('FlutterSecureStorage error on web: $e');
        return null;
      }
      rethrow;
    }
  }

  // Check if logged in
  Future<bool> isLoggedIn() async {
    try {
      final token = await getToken();
      return token != null;
    } catch (e) {
      debugPrint('Error checking login status: $e');
      return false;
    }
  }

  // Clear session
  Future<void> clearSession() async {
    try {
      await _storage.delete(key: 'authToken');
      await _storage.delete(key: 'refreshToken');
    } catch (e) {
      if (kIsWeb) {
        debugPrint('FlutterSecureStorage error on web: $e');
        // Even if deletion fails, we can consider the session cleared
        return;
      }
      rethrow;
    }
  }
}